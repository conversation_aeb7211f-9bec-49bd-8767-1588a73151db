module.exports = {
  apps: [
    {
      name: 'vereadora-rafaela',
      script: 'src/server.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        WPPCONNECT_PORT: 21465,
        WHATSAPP_SESSION_NAME: 'vereadora-rafaela',
        WPPCONNECT_SECRET_KEY: 'vereadora-rafaela-secret-2024',
        WEBHOOK_URL: 'http://localhost:3001/api/webhook/whatsapp'
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3001,
        WPPCONNECT_PORT: 21465,
        WHATSAPP_SESSION_NAME: 'vereadora-rafaela',
        WPPCONNECT_SECRET_KEY: 'vereadora-rafaela-secret-2024',
        WEBHOOK_URL: 'http://localhost:3001/api/webhook/whatsapp'
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true,
      merge_logs: true,
      max_restarts: 10,
      min_uptime: '10s',
      restart_delay: 4000,
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      wait_ready: true,
      ignore_watch: [
        'node_modules',
        'logs',
        'data',
        '.git'
      ],
      watch_options: {
        followSymlinks: false
      }
    }
  ],

  deploy: {
    production: {
      user: 'node',
      host: 'localhost',
      ref: 'origin/main',
      repo: '**************:ItaloCabral1995RN/AISTUDIOCHATRAG.git',
      path: '/var/www/vereadora-rafaela',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
