#!/usr/bin/env node

/**
 * Script para iniciar o sistema completo:
 * 1. WPPConnect Server (porta 21465)
 * 2. <PERSON><PERSON><PERSON> Principal (porta 3001)
 * 
 * Uso: node start-full-system.js
 */

import { spawn } from 'child_process';
import { Logger } from './src/utils/Logger.js';

const logger = new Logger();

class FullSystemStarter {
  constructor() {
    this.processes = [];
    this.isShuttingDown = false;
  }

  async start() {
    try {
      logger.info('🚀 Iniciando sistema completo...');
      
      // Configurar variáveis de ambiente
      process.env.WHATSAPP_USE_HTTP = 'true';
      process.env.WPPCONNECT_SERVER_URL = 'http://localhost:21465';
      process.env.WPPCONNECT_SECRET_KEY = 'vereadora-rafaela-secret-2024';
      process.env.WEBHOOK_URL = 'http://localhost:3001/api/webhook/whatsapp';
      
      logger.info('📊 Configurações:');
      logger.info('   WPPConnect Server: http://localhost:21465');
      logger.info('   Servidor Principal: http://localhost:3001');
      logger.info('   Webhook: http://localhost:3001/api/webhook/whatsapp');
      
      // Iniciar WPPConnect Server
      await this.startWPPConnectServer();
      
      // Aguardar 3 segundos para o WPPConnect Server inicializar
      logger.info('⏳ Aguardando WPPConnect Server inicializar...');
      await this.sleep(3000);
      
      // Iniciar Servidor Principal
      await this.startMainServer();
      
      // Configurar handlers de shutdown
      this.setupShutdownHandlers();
      
      logger.info('🎉 Sistema completo iniciado com sucesso!');
      logger.info('');
      logger.info('📱 Para conectar WhatsApp:');
      logger.info('   1. Acesse: http://localhost:3000');
      logger.info('   2. Vá para aba WhatsApp');
      logger.info('   3. Escaneie o QR Code');
      logger.info('');
      logger.info('🔧 APIs disponíveis:');
      logger.info('   WPPConnect: http://localhost:21465/api/health');
      logger.info('   Principal: http://localhost:3001/api/health');
      
    } catch (error) {
      logger.error('❌ Erro ao iniciar sistema completo:', error);
      await this.shutdown();
      process.exit(1);
    }
  }

  async startWPPConnectServer() {
    return new Promise((resolve, reject) => {
      logger.info('🔄 Iniciando WPPConnect Server...');
      
      const wppProcess = spawn('node', ['start-wppconnect.js'], {
        stdio: ['inherit', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      wppProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('WPPConnect Server iniciado')) {
          logger.info('✅ WPPConnect Server iniciado');
          resolve();
        }
        // Repassar logs com prefixo
        output.split('\n').forEach(line => {
          if (line.trim()) {
            logger.info(`[WPP] ${line.trim()}`);
          }
        });
      });

      wppProcess.stderr.on('data', (data) => {
        const output = data.toString();
        output.split('\n').forEach(line => {
          if (line.trim()) {
            logger.error(`[WPP] ${line.trim()}`);
          }
        });
      });

      wppProcess.on('close', (code) => {
        if (!this.isShuttingDown) {
          logger.error(`❌ WPPConnect Server encerrado com código ${code}`);
          reject(new Error(`WPPConnect Server falhou com código ${code}`));
        }
      });

      wppProcess.on('error', (error) => {
        logger.error('❌ Erro ao iniciar WPPConnect Server:', error);
        reject(error);
      });

      this.processes.push({
        name: 'WPPConnect Server',
        process: wppProcess
      });

      // Timeout de 10 segundos
      setTimeout(() => {
        if (wppProcess.exitCode === null) {
          resolve(); // Assumir que iniciou se ainda está rodando
        }
      }, 10000);
    });
  }

  async startMainServer() {
    return new Promise((resolve, reject) => {
      logger.info('🔄 Iniciando Servidor Principal...');
      
      const mainProcess = spawn('node', ['src/server.js'], {
        stdio: ['inherit', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      mainProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Servidor iniciado') || output.includes('listening on')) {
          logger.info('✅ Servidor Principal iniciado');
          resolve();
        }
        // Repassar logs com prefixo
        output.split('\n').forEach(line => {
          if (line.trim()) {
            logger.info(`[MAIN] ${line.trim()}`);
          }
        });
      });

      mainProcess.stderr.on('data', (data) => {
        const output = data.toString();
        output.split('\n').forEach(line => {
          if (line.trim()) {
            logger.error(`[MAIN] ${line.trim()}`);
          }
        });
      });

      mainProcess.on('close', (code) => {
        if (!this.isShuttingDown) {
          logger.error(`❌ Servidor Principal encerrado com código ${code}`);
          reject(new Error(`Servidor Principal falhou com código ${code}`));
        }
      });

      mainProcess.on('error', (error) => {
        logger.error('❌ Erro ao iniciar Servidor Principal:', error);
        reject(error);
      });

      this.processes.push({
        name: 'Servidor Principal',
        process: mainProcess
      });

      // Timeout de 15 segundos
      setTimeout(() => {
        if (mainProcess.exitCode === null) {
          resolve(); // Assumir que iniciou se ainda está rodando
        }
      }, 15000);
    });
  }

  setupShutdownHandlers() {
    const signals = ['SIGINT', 'SIGTERM'];
    
    signals.forEach(signal => {
      process.on(signal, async () => {
        logger.info(`🛑 Recebido ${signal}, encerrando sistema...`);
        await this.shutdown();
        process.exit(0);
      });
    });

    process.on('uncaughtException', async (error) => {
      logger.error('❌ Exceção não capturada:', error);
      await this.shutdown();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason) => {
      logger.error('❌ Promise rejeitada:', reason);
      await this.shutdown();
      process.exit(1);
    });
  }

  async shutdown() {
    if (this.isShuttingDown) return;
    
    this.isShuttingDown = true;
    logger.info('🛑 Encerrando todos os processos...');

    for (const { name, process } of this.processes) {
      try {
        logger.info(`🛑 Encerrando ${name}...`);
        
        if (process && !process.killed) {
          process.kill('SIGTERM');
          
          // Aguardar 5 segundos para encerramento graceful
          await Promise.race([
            new Promise(resolve => {
              process.on('close', resolve);
            }),
            this.sleep(5000)
          ]);
          
          // Force kill se ainda estiver rodando
          if (!process.killed) {
            process.kill('SIGKILL');
          }
        }
        
        logger.info(`✅ ${name} encerrado`);
      } catch (error) {
        logger.error(`❌ Erro ao encerrar ${name}:`, error.message);
      }
    }

    logger.info('✅ Sistema encerrado');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const starter = new FullSystemStarter();
  starter.start();
}

export default FullSystemStarter;
