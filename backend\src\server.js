import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Importar serviços
import { WhatsAppHttpService } from './services/WhatsAppHttpService.js';
import { WhatsAppSimulatorService } from './services/WhatsAppSimulatorService.js';
import { RAGService } from './services/RAGService.js';
import { MessageHandler } from './services/MessageHandler.js';
import { SessionManager } from './services/SessionManager.js';
import { PersistenceService } from './services/PersistenceService.js';
import { Logger } from './utils/Logger.js';
import { authenticateApiKey, verifyOrigin, rateLimitByIP, auditLog } from './middleware/auth.js';

// Importar rotas
import whatsappRoutes from './routes/whatsapp.js';
import sessionRoutes from './routes/session.js';
import webhookRoutes from './routes/webhook.js';
import healthRoutes from './routes/health.js';
import securityRoutes from './routes/security.js';
import antiBanRoutes from './routes/antiban.js';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '../.env') });

class Server {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3001;
    this.logger = new Logger();

    // Configurar limite de listeners para evitar memory leak
    process.setMaxListeners(20);

    // Inicializar serviços WhatsApp (usar simulador se Docker não disponível)
    this.whatsappService = this.createWhatsAppService();
    this.ragService = new RAGService();
    this.sessionManager = new SessionManager();
    this.persistenceService = new PersistenceService();
    this.messageHandler = new MessageHandler(this.whatsappService, this.ragService);

    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  createWhatsAppService() {
    // Verificar se deve usar simulador ou serviço real
    const useSimulator = process.env.WHATSAPP_USE_SIMULATOR === 'true' ||
                        process.env.NODE_ENV === 'development';

    if (useSimulator) {
      this.logger.info('🎭 Usando WhatsApp Simulator Service (modo desenvolvimento)');
      return new WhatsAppSimulatorService();
    } else {
      this.logger.info('📱 Usando WhatsApp HTTP Service (modo produção)');
      return new WhatsAppHttpService();
    }
  }

  setupMiddlewares() {
    // Middleware de auditoria
    this.app.use(auditLog);

    // Verificação de origem (apenas para APIs sensíveis) - Desabilitado para desenvolvimento
    // this.app.use('/api/whatsapp', verifyOrigin);
    this.app.use('/api/security', verifyOrigin);

    // Segurança
    this.app.use(helmet({
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false
    }));

    // CORS
    this.app.use(cors({
      origin: [
        process.env.FRONTEND_URL || 'http://localhost:3000',
        'http://localhost:3000',
        'http://127.0.0.1:3000'
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    }));

    // Compressão
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000,
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
      message: {
        error: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => this.logger.info(message.trim()) }
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Servir arquivos estáticos (QR codes, etc.)
    this.app.use('/static', express.static(join(__dirname, '../public')));
  }

  setupRoutes() {
    // Rota de boas-vindas
    this.app.get('/', (req, res) => {
      res.json({
        message: '🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda',
        version: '1.0.0',
        status: 'online',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/api/health',
          whatsapp: '/api/whatsapp',
          session: '/api/session',
          webhook: '/api/webhook'
        }
      });
    });

    // Rotas da API
    this.app.use('/api/health', healthRoutes);
    this.app.use('/api/whatsapp', whatsappRoutes); // Remover auth temporariamente para frontend
    this.app.use('/api/session', sessionRoutes); // Remover auth temporariamente para frontend
    this.app.use('/api/webhook', webhookRoutes);
    this.app.use('/api/security', authenticateApiKey(['admin']), securityRoutes);
    this.app.use('/api/antiban', authenticateApiKey(['whatsapp:read']), antiBanRoutes);

    // Rota 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint não encontrado',
        message: 'A rota solicitada não existe neste servidor',
        availableEndpoints: [
          '/api/health',
          '/api/whatsapp',
          '/api/session',
          '/api/webhook'
        ]
      });
    });
  }

  setupErrorHandling() {
    // Middleware de tratamento de erros
    this.app.use((error, req, res, next) => {
      this.logger.error('Erro no servidor:', error);

      // Erro de validação
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          error: 'Dados inválidos',
          message: error.message,
          code: 'VALIDATION_ERROR'
        });
      }

      // Erro de autenticação
      if (error.name === 'UnauthorizedError') {
        return res.status(401).json({
          error: 'Não autorizado',
          message: 'Token de acesso inválido ou expirado',
          code: 'UNAUTHORIZED'
        });
      }

      // Erro genérico
      res.status(500).json({
        error: 'Erro interno do servidor',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Algo deu errado',
        code: 'INTERNAL_SERVER_ERROR'
      });
    });

    // Remover listeners existentes para evitar duplicação
    process.removeAllListeners('unhandledRejection');
    process.removeAllListeners('uncaughtException');

    // Tratamento de promises rejeitadas
    process.once('unhandledRejection', (reason, promise) => {
      this.logger.error('Promise rejeitada não tratada:', reason);
    });

    // Tratamento de exceções não capturadas
    process.once('uncaughtException', (error) => {
      this.logger.error('Exceção não capturada:', error);
      process.exit(1);
    });
  }

  setupWhatsAppEventListeners() {
    // Listener para início da sincronização
    this.whatsappService.on('syncStarted', (data) => {
      this.logger.info('🔄 Sincronização automática iniciada');
      // Aqui você pode emitir via WebSocket se tiver configurado
    });

    // Listener para progresso da sincronização
    this.whatsappService.on('syncProgress', (data) => {
      this.logger.info(`📊 Progresso: ${data.current}/${data.total} - ${data.status}`);
      // Aqui você pode emitir via WebSocket se tiver configurado
    });

    // Listener para conclusão da sincronização
    this.whatsappService.on('syncCompleted', (data) => {
      this.logger.info('✅ Sincronização automática concluída');
      // Aqui você pode emitir via WebSocket se tiver configurado
    });

    // Listener para erro na sincronização
    this.whatsappService.on('syncError', (data) => {
      this.logger.error('❌ Erro na sincronização automática:', data.error);
      // Aqui você pode emitir via WebSocket se tiver configurado
    });
  }

  async start() {
    try {
      // Inicializar serviços
      await this.initializeServices();

      // Iniciar servidor
      this.app.listen(this.port, () => {
        this.logger.info(`🚀 Servidor iniciado na porta ${this.port}`);
        this.logger.info(`🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda`);
        this.logger.info(`📱 Ambiente: ${process.env.NODE_ENV || 'development'}`);
        this.logger.info(`🌐 URL: http://localhost:${this.port}`);
      });

    } catch (error) {
      this.logger.error('Erro ao iniciar servidor:', error);
      // Não fazer exit para permitir que o servidor continue funcionando
      this.logger.warn('⚠️ Servidor iniciado com funcionalidades limitadas');

      this.app.listen(this.port, () => {
        this.logger.info(`🚀 Servidor iniciado na porta ${this.port} (modo limitado)`);
        this.logger.info(`🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda`);
        this.logger.info(`📱 Ambiente: ${process.env.NODE_ENV || 'development'}`);
        this.logger.info(`🌐 URL: http://localhost:${this.port}`);
      });
    }
  }

  async initializeServices() {
    this.logger.info('🔧 Inicializando serviços...');

    try {
      // Disponibilizar serviços para as rotas
      this.app.locals.whatsappService = this.whatsappService;
      this.app.locals.ragService = this.ragService;
      this.app.locals.messageHandler = this.messageHandler;
      this.app.locals.sessionManager = this.sessionManager;
      this.app.locals.persistenceService = this.persistenceService;

      // Inicializar Persistence Service primeiro
      try {
        await this.persistenceService.initialize();
        this.logger.info('✅ Persistence Service inicializado');
      } catch (error) {
        this.logger.error('❌ Erro ao inicializar Persistence Service:', error.message);
      }

      // Inicializar WhatsApp Service (não crítico)
      try {
        const whatsappInitialized = await this.whatsappService.initialize();
        if (whatsappInitialized) {
          this.logger.info('✅ WhatsApp Service inicializado');
          // Configurar listeners de eventos do WhatsApp apenas se inicializado
          this.setupWhatsAppEventListeners();
        } else {
          this.logger.warn('⚠️ WhatsApp Service em modo degradado');
        }
      } catch (error) {
        this.logger.error('❌ Erro ao inicializar WhatsApp Service:', error.message);
        this.logger.warn('⚠️ Continuando sem WhatsApp Service');
      }

      // Inicializar RAG Service
      try {
        await this.ragService.initialize();
        this.logger.info('✅ RAG Service inicializado');
      } catch (error) {
        this.logger.error('❌ Erro ao inicializar RAG Service:', error.message);
      }

      // Inicializar Session Manager
      try {
        await this.sessionManager.initialize();
        this.logger.info('✅ Session Manager inicializado');
      } catch (error) {
        this.logger.error('❌ Erro ao inicializar Session Manager:', error.message);
      }

      // Configurar handlers de mensagem
      try {
        this.messageHandler.setupHandlers();
        this.logger.info('✅ Message Handlers configurados');
      } catch (error) {
        this.logger.error('❌ Erro ao configurar Message Handlers:', error.message);
      }

      this.logger.info('🎉 Inicialização de serviços concluída!');

    } catch (error) {
      this.logger.error('❌ Erro crítico ao inicializar serviços:', error.message);
      // Não fazer throw para permitir que o servidor continue funcionando
      this.logger.warn('⚠️ Servidor funcionará com funcionalidades limitadas');
    }
  }

  async stop() {
    this.logger.info('🛑 Parando servidor...');
    
    try {
      // Parar serviços
      await this.whatsappService.stop();
      await this.sessionManager.cleanup();
      
      this.logger.info('✅ Servidor parado com sucesso');
    } catch (error) {
      this.logger.error('❌ Erro ao parar servidor:', error);
    }
  }
}

// Inicializar e iniciar servidor
const server = new Server();

// Graceful shutdown
process.on('SIGTERM', async () => {
  await server.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  await server.stop();
  process.exit(0);
});

// Iniciar servidor
server.start().catch((error) => {
  console.error('Falha ao iniciar servidor:', error);
  process.exit(1);
});

export { server as default, Server };
