import React, { useState, useEffect } from 'react';
import { whatsappService, WhatsAppStats, SendMessageRequest } from '../services/whatsappService';
import { WhatsAppQRCode } from './WhatsAppQRCode';
import { RafaelaResponseTester } from './RafaelaResponseTester';

export const WhatsAppManager: React.FC = () => {
  const [stats, setStats] = useState<WhatsAppStats | null>(null);
  const [chats, setChats] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'qrcode' | 'chats' | 'rafaela' | 'send' | 'simulator'>('qrcode');
  const [isSimulator, setIsSimulator] = useState(false);
  const [sendMessage, setSendMessage] = useState<SendMessageRequest>({
    to: '',
    message: '',
    type: 'text'
  });
  const [sendResult, setSendResult] = useState<string | null>(null);

  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 10000); // Atualizar a cada 10s
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Carregar apenas o status básico por enquanto
      const statusData = await whatsappService.getStatus();

      if (statusData) {
        // Verificar se é simulador
        setIsSimulator(statusData.isSimulator || false);

        setStats({
          connection: statusData,
          messages: {
            activeSessions: 0,
            totalMessages: 0,
            rateLimitedUsers: 0,
            autoReplyEnabled: false,
            businessHoursOnly: false,
            isBusinessHours: false
          },
          chats: { total: 0, groups: 0, private: 0 },
          uptime: 0,
          lastActivity: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('Erro ao carregar dados WhatsApp:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!sendMessage.to || !sendMessage.message) {
      setSendResult('❌ Preencha número e mensagem');
      return;
    }

    try {
      setSendResult('📤 Enviando...');
      const result = await whatsappService.sendMessage(sendMessage);
      
      if (result.success) {
        setSendResult('✅ Mensagem enviada com sucesso!');
        setSendMessage({ to: '', message: '', type: 'text' });
      } else {
        setSendResult(`❌ Erro: ${result.error}`);
      }
    } catch (error) {
      setSendResult('❌ Erro ao enviar mensagem');
    }

    // Limpar resultado após 5 segundos
    setTimeout(() => setSendResult(null), 5000);
  };

  const formatPhoneNumber = (phone: string) => {
    return phone.replace('@c.us', '').replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  const tabs = [
    { id: 'qrcode', label: '📱 Conectar', icon: '📱' },
    { id: 'overview', label: '📊 Visão Geral', icon: '📊' },
    { id: 'chats', label: '💬 Conversas', icon: '💬' },
    { id: 'rafaela', label: '🤖 Testar Rafaela', icon: '🤖' },
    { id: 'send', label: '📤 Enviar', icon: '📤' },
    ...(isSimulator ? [{ id: 'simulator', label: '🎭 Simulador', icon: '🎭' }] : [])
  ];

  if (loading && !stats) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="loading-modern mb-4">
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
          </div>
          <p className="text-gray-600">Carregando dados do WhatsApp...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header com Tabs */}
      <div className="border-b border-white/20 bg-white/60 backdrop-blur-sm">
        <div className="flex space-x-1 p-4">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-orange-500 text-white shadow-lg'
                  : 'bg-white/30 text-gray-700 hover:bg-white/50'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Conteúdo */}
      <div className="flex-1 p-6 overflow-auto">
        {activeTab === 'qrcode' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">📱 Conectar WhatsApp</h2>
            <WhatsAppQRCode onConnectionChange={(connected) => {
              if (connected) {
                loadData(); // Recarregar dados quando conectar
              }
            }} />
          </div>
        )}

        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">📊 Visão Geral do WhatsApp</h2>
            
            {stats ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Status da Conexão */}
                <div className="glass-card p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className={`text-2xl ${stats.connection.isConnected ? 'text-green-500' : 'text-red-500'}`}>
                      {stats.connection.isConnected ? '🟢' : '🔴'}
                    </span>
                    <div>
                      <h3 className="font-medium text-gray-800">Conexão</h3>
                      <p className="text-sm text-gray-600">
                        {stats.connection.isConnected ? 'Conectado' : 'Desconectado'}
                      </p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>Sessão: {stats.connection.sessionName}</p>
                    <p>Tentativas: {stats.connection.connectionAttempts}</p>
                  </div>
                </div>

                {/* Estatísticas de Mensagens */}
                <div className="glass-card p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">💬</span>
                    <div>
                      <h3 className="font-medium text-gray-800">Mensagens</h3>
                      <p className="text-sm text-gray-600">{stats.messages.totalMessages} total</p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>Sessões ativas: {stats.messages.activeSessions}</p>
                    <p>Rate limited: {stats.messages.rateLimitedUsers}</p>
                  </div>
                </div>

                {/* Sistema */}
                <div className="glass-card p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">⚙️</span>
                    <div>
                      <h3 className="font-medium text-gray-800">Sistema</h3>
                      <p className="text-sm text-gray-600">
                        Uptime: {Math.floor(stats.uptime / 3600)}h {Math.floor((stats.uptime % 3600) / 60)}m
                      </p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>Auto-reply: {stats.messages.autoReplyEnabled ? '✅' : '❌'}</p>
                    <p>Horário comercial: {stats.messages.isBusinessHours ? '🟢' : '🟡'}</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="glass-card p-8 text-center">
                <span className="text-4xl mb-4 block">📱</span>
                <h3 className="text-lg font-medium text-gray-800 mb-2">WhatsApp Backend Offline</h3>
                <p className="text-gray-600 mb-4">
                  O backend WhatsApp não está disponível. Inicie o servidor para usar esta funcionalidade.
                </p>
                <div className="text-sm text-gray-500 space-y-1">
                  <p>1. Vá para a pasta backend/</p>
                  <p>2. Execute: npm run dev</p>
                  <p>3. O backend rodará na porta 3001</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'chats' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-800">💬 Conversas Ativas</h2>
              <button
                onClick={loadData}
                className="px-3 py-1 text-sm bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
              >
                🔄 Atualizar
              </button>
            </div>

            {chats.length > 0 ? (
              <div className="space-y-2">
                {chats.map((chat, index) => (
                  <div key={chat.id || index} className="glass-card p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">
                          {chat.isGroup ? '👥' : '👤'}
                        </span>
                        <div>
                          <h3 className="font-medium text-gray-800">{chat.name || 'Sem nome'}</h3>
                          <p className="text-sm text-gray-600">
                            {chat.isGroup ? 'Grupo' : 'Contato individual'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        {chat.unreadCount > 0 && (
                          <span className="px-2 py-1 text-xs bg-orange-500 text-white rounded-full">
                            {chat.unreadCount}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="glass-card p-8 text-center">
                <span className="text-4xl mb-4 block">💬</span>
                <p className="text-gray-600">Nenhuma conversa encontrada</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'rafaela' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">🤖 Testador de Respostas da Vereadora Rafaela</h2>
            <RafaelaResponseTester />
          </div>
        )}

        {activeTab === 'send' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">📤 Enviar Mensagem</h2>
            
            <div className="glass-card p-6 max-w-2xl">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Número de Destino
                  </label>
                  <input
                    type="text"
                    value={sendMessage.to}
                    onChange={(e) => setSendMessage(prev => ({ ...prev, to: e.target.value }))}
                    placeholder="5584999999999 (com código do país)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Formato: código do país + DDD + número (ex: 5584999999999)
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mensagem
                  </label>
                  <textarea
                    value={sendMessage.message}
                    onChange={(e) => setSendMessage(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Digite sua mensagem aqui..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={handleSendMessage}
                    disabled={!sendMessage.to || !sendMessage.message || !stats?.connection.isConnected}
                    className="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    📤 Enviar Mensagem
                  </button>
                  
                  <button
                    onClick={() => setSendMessage({ to: '', message: '', type: 'text' })}
                    className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    🗑️ Limpar
                  </button>
                </div>

                {sendResult && (
                  <div className={`p-3 rounded-lg ${
                    sendResult.includes('✅') ? 'bg-green-100 text-green-800' : 
                    sendResult.includes('📤') ? 'bg-blue-100 text-blue-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {sendResult}
                  </div>
                )}

                {!stats?.connection.isConnected && (
                  <div className="p-3 bg-orange-100 text-orange-800 rounded-lg">
                    ⚠️ WhatsApp não está conectado. Conecte primeiro para enviar mensagens.
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'simulator' && isSimulator && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">🎭 Controles do Simulador</h2>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-blue-600">ℹ️</span>
                <span className="font-medium text-blue-800">Modo Simulador Ativo</span>
              </div>
              <p className="text-blue-700 text-sm">
                Você está usando o WhatsApp Simulator. Todas as funcionalidades são simuladas para desenvolvimento.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Controles de Conexão */}
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h3 className="font-semibold text-gray-800 mb-4">🔌 Controles de Conexão</h3>

                <div className="space-y-3">
                  <button
                    onClick={async () => {
                      const success = await whatsappService.simulatorConnect();
                      if (success) {
                        setSendResult('✅ Conexão simulada iniciada');
                        setTimeout(() => loadData(), 2000);
                      } else {
                        setSendResult('❌ Erro ao simular conexão');
                      }
                      setTimeout(() => setSendResult(null), 3000);
                    }}
                    className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                  >
                    🔗 Simular Conexão
                  </button>

                  <button
                    onClick={async () => {
                      const success = await whatsappService.simulatorDisconnect();
                      if (success) {
                        setSendResult('✅ Desconexão simulada');
                        setTimeout(() => loadData(), 2000);
                      } else {
                        setSendResult('❌ Erro ao simular desconexão');
                      }
                      setTimeout(() => setSendResult(null), 3000);
                    }}
                    className="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                  >
                    🔌 Simular Desconexão
                  </button>
                </div>
              </div>

              {/* Simular Mensagem Recebida */}
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h3 className="font-semibold text-gray-800 mb-4">📨 Simular Mensagem Recebida</h3>

                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nome do Remetente
                    </label>
                    <input
                      type="text"
                      placeholder="Ex: João Silva"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      id="sim-sender-name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Número (opcional)
                    </label>
                    <input
                      type="text"
                      placeholder="Ex: <EMAIL>"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      id="sim-sender-phone"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Mensagem
                    </label>
                    <textarea
                      placeholder="Digite a mensagem que será simulada..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      id="sim-message"
                    />
                  </div>

                  <button
                    onClick={async () => {
                      const nameInput = document.getElementById('sim-sender-name') as HTMLInputElement;
                      const phoneInput = document.getElementById('sim-sender-phone') as HTMLInputElement;
                      const messageInput = document.getElementById('sim-message') as HTMLTextAreaElement;

                      const name = nameInput.value || 'Usuário Teste';
                      const phone = phoneInput.value || `5584${Math.floor(Math.random() * 100000000)}@c.us`;
                      const message = messageInput.value;

                      if (!message.trim()) {
                        setSendResult('❌ Digite uma mensagem');
                        setTimeout(() => setSendResult(null), 3000);
                        return;
                      }

                      const success = await whatsappService.simulatorSendMessage(phone, message, name);
                      if (success) {
                        setSendResult(`✅ Mensagem simulada de ${name}`);
                        // Limpar campos
                        nameInput.value = '';
                        phoneInput.value = '';
                        messageInput.value = '';
                      } else {
                        setSendResult('❌ Erro ao simular mensagem');
                      }
                      setTimeout(() => setSendResult(null), 5000);
                    }}
                    className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    📨 Enviar Mensagem Simulada
                  </button>
                </div>
              </div>
            </div>

            {/* Mensagens Pré-definidas */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="font-semibold text-gray-800 mb-4">💬 Mensagens Rápidas</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {[
                  { name: 'Maria Silva', message: 'Olá! Preciso de informações sobre saúde pública.' },
                  { name: 'João Santos', message: 'Bom dia! Como posso agendar uma consulta?' },
                  { name: 'Ana Costa', message: 'Existe algum programa de auxílio para famílias?' },
                  { name: 'Pedro Lima', message: 'Gostaria de saber sobre os projetos da vereadora.' },
                  { name: 'Carla Oliveira', message: 'Como faço para solicitar melhorias no meu bairro?' },
                  { name: 'Roberto Silva', message: 'Preciso de ajuda com documentação.' }
                ].map((preset, index) => (
                  <button
                    key={index}
                    onClick={async () => {
                      const phone = `5584${Math.floor(Math.random() * 100000000)}@c.us`;
                      const success = await whatsappService.simulatorSendMessage(phone, preset.message, preset.name);
                      if (success) {
                        setSendResult(`✅ Mensagem de ${preset.name} simulada`);
                      } else {
                        setSendResult('❌ Erro ao simular mensagem');
                      }
                      setTimeout(() => setSendResult(null), 3000);
                    }}
                    className="p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors"
                  >
                    <div className="font-medium text-sm text-gray-800">{preset.name}</div>
                    <div className="text-xs text-gray-600 mt-1 line-clamp-2">{preset.message}</div>
                  </button>
                ))}
              </div>
            </div>

            {sendResult && (
              <div className={`p-3 rounded-lg ${
                sendResult.includes('✅')
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {sendResult}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
