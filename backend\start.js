#!/usr/bin/env node

/**
 * Script de inicialização simplificado para Vereadora Rafaela
 * Gerencia automaticamente PM2 e fornece interface amigável
 */

import { execSync, spawn } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

class VeradoraRafaelaManager {
  constructor() {
    this.processName = 'vereadora-rafaela';
    this.configFile = 'ecosystem.config.cjs';
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      warning: '\x1b[33m', // Yellow
      error: '\x1b[31m',   // Red
      reset: '\x1b[0m'     // Reset
    };
    
    console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
  }

  async checkPM2() {
    try {
      execSync('pm2 --version', { stdio: 'ignore' });
      return true;
    } catch {
      this.log('PM2 não está instalado. Instalando...', 'warning');
      try {
        execSync('npm install -g pm2', { stdio: 'inherit' });
        this.log('PM2 instalado com sucesso!', 'success');
        return true;
      } catch {
        this.log('Erro ao instalar PM2', 'error');
        return false;
      }
    }
  }

  async getProcessStatus() {
    try {
      const output = execSync(`pm2 jlist`, { encoding: 'utf8' });
      const processes = JSON.parse(output);
      return processes.find(p => p.name === this.processName);
    } catch {
      return null;
    }
  }

  async start() {
    this.log('🚀 Iniciando Vereadora Rafaela...', 'info');

    // Verificar PM2
    if (!(await this.checkPM2())) {
      process.exit(1);
    }

    // Verificar se já está rodando
    const process = await this.getProcessStatus();
    
    if (process && process.pm2_env.status === 'online') {
      this.log('✅ Servidor já está rodando!', 'success');
      this.showStatus();
      return;
    }

    // Verificar arquivo de configuração
    if (!existsSync(this.configFile)) {
      this.log(`❌ Arquivo ${this.configFile} não encontrado`, 'error');
      process.exit(1);
    }

    try {
      // Iniciar com PM2
      execSync(`pm2 start ${this.configFile}`, { stdio: 'inherit' });
      this.log('✅ Servidor iniciado com sucesso!', 'success');
      
      // Aguardar um pouco e mostrar status
      setTimeout(() => {
        this.showStatus();
      }, 2000);

    } catch (error) {
      this.log('❌ Erro ao iniciar servidor', 'error');
      console.error(error.message);
      process.exit(1);
    }
  }

  async stop() {
    this.log('🛑 Parando Vereadora Rafaela...', 'warning');
    
    try {
      execSync(`pm2 stop ${this.processName}`, { stdio: 'inherit' });
      this.log('✅ Servidor parado com sucesso!', 'success');
    } catch (error) {
      this.log('❌ Erro ao parar servidor', 'error');
      console.error(error.message);
    }
  }

  async restart() {
    this.log('🔄 Reiniciando Vereadora Rafaela...', 'info');
    
    try {
      execSync(`pm2 restart ${this.processName}`, { stdio: 'inherit' });
      this.log('✅ Servidor reiniciado com sucesso!', 'success');
      
      setTimeout(() => {
        this.showStatus();
      }, 2000);
    } catch (error) {
      this.log('❌ Erro ao reiniciar servidor', 'error');
      console.error(error.message);
    }
  }

  async showStatus() {
    try {
      this.log('📊 Status do sistema:', 'info');
      execSync(`pm2 status`, { stdio: 'inherit' });
      
      this.log('\n🌐 URLs disponíveis:', 'info');
      console.log('  • Frontend: http://localhost:3000');
      console.log('  • API: http://localhost:3001');
      console.log('  • WhatsApp: http://localhost:21465');
      console.log('  • Health: http://localhost:3001/api/health');
      console.log('  • WhatsApp Status: http://localhost:3001/api/whatsapp/status');
      
    } catch (error) {
      this.log('❌ Erro ao obter status', 'error');
    }
  }

  async logs() {
    this.log('📋 Mostrando logs...', 'info');
    
    try {
      const child = spawn('pm2', ['logs', this.processName], { 
        stdio: 'inherit' 
      });
      
      process.on('SIGINT', () => {
        child.kill();
        process.exit(0);
      });
      
    } catch (error) {
      this.log('❌ Erro ao mostrar logs', 'error');
    }
  }

  showHelp() {
    console.log(`
🏛️  Vereadora Rafaela - Sistema de Gerenciamento

📋 Comandos disponíveis:
  start     Iniciar o servidor
  stop      Parar o servidor  
  restart   Reiniciar o servidor
  status    Mostrar status do sistema
  logs      Mostrar logs em tempo real
  help      Mostrar esta ajuda

💡 Exemplos:
  node start.js start
  node start.js status
  node start.js logs
    `);
  }
}

// Executar comando
const manager = new VeradoraRafaelaManager();
const command = process.argv[2] || 'help';

switch (command) {
  case 'start':
    manager.start();
    break;
  case 'stop':
    manager.stop();
    break;
  case 'restart':
    manager.restart();
    break;
  case 'status':
    manager.showStatus();
    break;
  case 'logs':
    manager.logs();
    break;
  case 'help':
  default:
    manager.showHelp();
    break;
}
