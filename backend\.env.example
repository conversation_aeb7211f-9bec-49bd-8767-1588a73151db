# Configurações do Servidor
PORT=3001
NODE_ENV=development

# Configurações do WhatsApp
WHATSAPP_SESSION_NAME=vereadora-rafaela
WHATSAPP_AUTO_CLOSE=false
WHATSAPP_DISABLE_WELCOME=false

# WhatsApp Service Selection
# WHATSAPP_USE_SIMULATOR=true    # Para desenvolvimento (simulador)
WHATSAPP_USE_REAL=true           # Para produção (WPPConnect direto)
# WHATSAPP_USE_HTTP=true         # Para produção (WPPConnect Server)

# WPPConnect Server (apenas se usar HTTP)
WPPCONNECT_SERVER_URL=http://localhost:21465
WPPCONNECT_SECRET_KEY=vereadora-rafaela-secret-2024

# Configurações do Frontend (Sistema RAG)
FRONTEND_URL=http://localhost:3000
RAG_API_URL=http://localhost:3000/api

# Configurações de Segurança
JWT_SECRET=sua_chave_secreta_muito_forte_aqui
API_KEY=sua_api_key_para_autenticacao

# Chaves de API específicas
FRONTEND_API_KEY=frontend-key-secure-random-string
ADMIN_API_KEY=admin-key-secure-random-string

# Configurações de Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Configurações de Log
LOG_LEVEL=info
LOG_FILE=logs/whatsapp-backend.log

# Configurações da Vereadora
VEREADORA_NAME=Rafaela de Nilda
VEREADORA_TITLE=Vereadora
MUNICIPIO=Parnamirim
ESTADO=RN
GABINETE_TELEFONE=(84) 99999-9999
GABINETE_EMAIL=<EMAIL>

# Configurações de Horário de Atendimento
HORARIO_INICIO=08:00
HORARIO_FIM=18:00
DIAS_FUNCIONAMENTO=1,2,3,4,5

# Configurações de Mensagens Automáticas
AUTO_REPLY_ENABLED=true
WELCOME_MESSAGE_ENABLED=true
BUSINESS_HOURS_ONLY=false

# Configurações de Webhook (opcional)
WEBHOOK_URL=
WEBHOOK_SECRET=

# Configurações de Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
AUTO_BACKUP_ENABLED=true

# Configurações de Reconexão
MAX_RECONNECT_ATTEMPTS=10
RECONNECT_DELAY_MS=30000
MAX_RECONNECT_DELAY_MS=300000

# Configurações de Monitoramento
HEALTH_CHECK_INTERVAL_MS=30000
CONNECTION_QUALITY_THRESHOLD_MS=5000

# Configurações de Persistência
ENCRYPTION_ENABLED=true
INTEGRITY_CHECKS=true
BACKUP_RETENTION_DAYS=30
