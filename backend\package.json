{"name": "vereadora-rafaela-whatsapp-backend", "version": "1.0.0", "description": "Backend WhatsApp para Assistente Virtual da Vereadora Rafaela de Nilda", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "node --watch src/server.js", "dev:node": "node src/server.js", "dev:full": "npm run check:wppconnect && npm run dev", "build": "echo 'No build step required'", "test": "echo 'No tests specified'", "check:wppconnect": "node scripts/check-wppconnect.js check", "status:wppconnect": "node scripts/check-wppconnect.js status", "start:wppconnect": "node scripts/check-wppconnect.js start", "wppconnect:server": "node start-wppconnect.js", "wppconnect:dev": "node --watch start-wppconnect.js", "start:full": "node start-full-system.js", "start:full:concurrent": "concurrently \"npm run wppconnect:server\" \"npm run start\"", "verify": "node scripts/verify-system.js", "verify:system": "node scripts/verify-system.js"}, "keywords": ["whatsapp", "wppconnect", "chatbot", "vereadora", "parnam<PERSON>m", "rag", "ai"], "author": "Sistema RAG Vereadora Rafaela de Nilda", "license": "MIT", "dependencies": {"axios": "^1.6.2", "compression": "^1.7.4", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "qrcode": "^1.5.3", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}