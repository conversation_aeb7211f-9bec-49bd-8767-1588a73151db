import express from 'express';
import { WhatsAppSimulatorService } from './src/services/WhatsAppSimulatorService.js';

const app = express();
const port = 3003;

// Middlewares básicos
app.use(express.json());

// Criar simulador
const whatsappService = new WhatsAppSimulatorService();

// Disponibilizar para as rotas
app.locals.whatsappService = whatsappService;

// Rotas básicas
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

app.get('/api/whatsapp/status', (req, res) => {
  try {
    const status = whatsappService.getConnectionStatus();
    status.serviceType = 'simulator';
    status.isSimulator = true;
    
    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Erro interno',
      message: error.message
    });
  }
});

app.post('/api/whatsapp/simulator/connect', async (req, res) => {
  try {
    await whatsappService.simulateConnection();
    res.json({
      success: true,
      message: 'Conexão simulada iniciada',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Erro interno',
      message: error.message
    });
  }
});

app.post('/api/whatsapp/simulator/disconnect', async (req, res) => {
  try {
    whatsappService.simulateDisconnection();
    res.json({
      success: true,
      message: 'Desconexão simulada',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Erro interno',
      message: error.message
    });
  }
});

app.post('/api/whatsapp/simulator/message', async (req, res) => {
  try {
    const { from, message, name } = req.body;
    
    if (!from || !message) {
      return res.status(400).json({
        error: 'Dados inválidos',
        message: 'from e message são obrigatórios'
      });
    }
    
    const simulatedMessage = {
      id: `sim_manual_${Date.now()}`,
      from: from,
      body: message,
      timestamp: Date.now(),
      isFromMe: false,
      notifyName: name || 'Usuário Teste',
      type: 'chat'
    };
    
    if (whatsappService.onMessage) {
      whatsappService.onMessage(simulatedMessage);
    }
    
    res.json({
      success: true,
      message: 'Mensagem simulada enviada',
      data: simulatedMessage,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Erro interno',
      message: error.message
    });
  }
});

// Inicializar
async function init() {
  try {
    console.log('🔄 Inicializando servidor simples...');
    
    // Inicializar simulador
    await whatsappService.initialize();
    console.log('✅ WhatsApp Simulator inicializado');
    
    // Iniciar servidor
    app.listen(port, () => {
      console.log(`🚀 Servidor simples rodando na porta ${port}`);
      console.log(`🌐 URL: http://localhost:${port}`);
      console.log(`📊 Health: http://localhost:${port}/api/health`);
      console.log(`📱 WhatsApp Status: http://localhost:${port}/api/whatsapp/status`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao inicializar servidor simples:', error);
    process.exit(1);
  }
}

init();
