import express from 'express';
import cors from 'cors';
import wppconnect from '@wppconnect-team/wppconnect';
import { Logger } from './src/utils/Logger.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * WPPConnect Server - Servidor HTTP para gerenciar sessões WhatsApp
 * Implementa API REST para integração com chatbots
 */
class WPPConnectServer {
  constructor() {
    this.app = express();
    this.port = process.env.WPPCONNECT_PORT || 21465;
    this.logger = new Logger();
    this.sessions = new Map(); // Armazena as sessões ativas
    this.webhookUrl = process.env.WEBHOOK_URL || 'http://localhost:3001/api/webhook/whatsapp';
    this.secretKey = process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024';
    
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupAuthMiddleware();
    this.setupDirectories();
  }

  setupDirectories() {
    const dirs = ['data/tokens', 'data/sessions', 'data/downloads', 'data/uploads'];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        this.logger.info(`📁 Diretório criado: ${dir}`);
      }
    });
  }

  setupMiddlewares() {
    this.app.use(cors());
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // Middleware de logging
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path} - ${req.ip}`);
      next();
    });
  }

  setupAuthMiddleware() {
    // Middleware de autenticação para rotas de sessão
    this.app.use('/api/:session', (req, res, next) => {
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];

      if (!token || token !== this.secretKey) {
        return res.status(401).json({
          success: false,
          error: 'Token de autorização inválido'
        });
      }

      next();
    });
  }

  setupRoutes() {
    // Health check (sem autenticação)
    this.app.get('/api/health', (req, res) => {
      res.json({
        success: true,
        message: 'WPPConnect Server está funcionando',
        timestamp: new Date().toISOString(),
        sessions: Array.from(this.sessions.keys()),
        version: '1.0.0'
      });
    });

    // Criar/Iniciar sessão
    this.app.post('/api/:session/start-session', async (req, res) => {
      const sessionName = req.params.session;
      
      try {
        if (this.sessions.has(sessionName)) {
          return res.json({
            success: true,
            message: 'Sessão já está ativa',
            session: sessionName
          });
        }

        await this.createSession(sessionName);
        
        res.json({
          success: true,
          message: 'Sessão iniciada com sucesso',
          session: sessionName
        });

      } catch (error) {
        this.logger.error(`Erro ao iniciar sessão ${sessionName}:`, error.message);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Status da sessão
    this.app.get('/api/:session/status', (req, res) => {
      const sessionName = req.params.session;
      const session = this.sessions.get(sessionName);
      
      if (!session) {
        return res.json({
          success: false,
          message: 'Sessão não encontrada',
          status: 'DISCONNECTED'
        });
      }

      res.json({
        success: true,
        session: sessionName,
        status: session.status || 'UNKNOWN',
        qrCode: session.qrCode || null,
        timestamp: new Date().toISOString()
      });
    });

    // Enviar mensagem
    this.app.post('/api/:session/send-message', async (req, res) => {
      const sessionName = req.params.session;
      const { phone, message, isGroup = false } = req.body;
      
      try {
        const session = this.sessions.get(sessionName);
        
        if (!session || !session.client) {
          return res.status(400).json({
            success: false,
            error: 'Sessão não está conectada'
          });
        }

        const result = await session.client.sendText(phone, message);
        
        this.logger.info(`📤 Mensagem enviada para ${phone}: ${message}`);
        
        res.json({
          success: true,
          messageId: result.id,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        this.logger.error(`Erro ao enviar mensagem:`, error.message);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Fechar sessão
    this.app.post('/api/:session/close-session', async (req, res) => {
      const sessionName = req.params.session;
      
      try {
        await this.closeSession(sessionName);
        
        res.json({
          success: true,
          message: 'Sessão fechada com sucesso'
        });

      } catch (error) {
        this.logger.error(`Erro ao fechar sessão:`, error.message);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
  }

  async createSession(sessionName) {
    this.logger.info(`🔄 Criando sessão: ${sessionName}`);
    
    const sessionData = {
      name: sessionName,
      status: 'INITIALIZING',
      qrCode: null,
      client: null,
      createdAt: new Date().toISOString()
    };

    this.sessions.set(sessionName, sessionData);

    try {
      const client = await wppconnect.create({
        session: sessionName,
        catchQR: (base64Qr, asciiQR) => {
          this.logger.info(`📱 QR Code gerado para sessão: ${sessionName}`);
          sessionData.qrCode = {
            base64: base64Qr,
            ascii: asciiQR,
            timestamp: new Date().toISOString()
          };
          sessionData.status = 'WAITING_QR';
        },
        statusFind: (statusSession, session) => {
          this.logger.info(`📊 Status da sessão ${sessionName}: ${statusSession}`);
          sessionData.status = statusSession;
          
          if (statusSession === 'isLogged') {
            sessionData.qrCode = null;
            this.logger.info(`✅ Sessão ${sessionName} conectada com sucesso!`);
          }
        },
        headless: true,
        devtools: false,
        useChrome: true,
        debug: false,
        logQR: true,
        folderNameToken: 'data/tokens',
        mkdirFolderToken: 'data/tokens',
        updatesLog: true,
        autoClose: 60000,
        createPathFileToken: true
      });

      sessionData.client = client;
      sessionData.status = 'CONNECTED';

      // Configurar listener para mensagens recebidas
      client.onMessage(async (message) => {
        await this.handleIncomingMessage(sessionName, message);
      });

      this.logger.info(`✅ Sessão ${sessionName} criada com sucesso`);

    } catch (error) {
      this.logger.error(`❌ Erro ao criar sessão ${sessionName}:`, error.message);
      this.sessions.delete(sessionName);
      throw error;
    }
  }

  async handleIncomingMessage(sessionName, message) {
    try {
      this.logger.info(`📨 Mensagem recebida na sessão ${sessionName} de ${message.from}: ${message.body}`);
      
      // Enviar mensagem para o webhook do chatbot
      if (this.webhookUrl) {
        const { default: fetch } = await import('node-fetch');
        
        const webhookData = {
          session: sessionName,
          message: {
            id: message.id,
            from: message.from,
            body: message.body,
            timestamp: message.timestamp * 1000,
            isFromMe: message.fromMe,
            notifyName: message.notifyName || message.sender?.pushname,
            type: message.type
          },
          timestamp: new Date().toISOString()
        };

        await fetch(this.webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.secretKey}`
          },
          body: JSON.stringify(webhookData)
        });

        this.logger.info(`📡 Mensagem enviada para webhook: ${this.webhookUrl}`);
      }

    } catch (error) {
      this.logger.error(`❌ Erro ao processar mensagem recebida:`, error.message);
    }
  }

  async closeSession(sessionName) {
    const session = this.sessions.get(sessionName);
    
    if (session && session.client) {
      try {
        await session.client.close();
        this.logger.info(`✅ Sessão ${sessionName} fechada`);
      } catch (error) {
        this.logger.error(`❌ Erro ao fechar sessão ${sessionName}:`, error.message);
      }
    }
    
    this.sessions.delete(sessionName);
  }

  async start() {
    try {
      this.app.listen(this.port, () => {
        this.logger.info(`🚀 WPPConnect Server iniciado na porta ${this.port}`);
        this.logger.info(`🌐 URL: http://localhost:${this.port}`);
        this.logger.info(`📊 Health: http://localhost:${this.port}/api/health`);
        this.logger.info(`🔑 Secret Key: ${this.secretKey.substring(0, 10)}...`);
        this.logger.info(`📡 Webhook URL: ${this.webhookUrl}`);
      });

      // Graceful shutdown
      process.on('SIGINT', async () => {
        this.logger.info('🛑 Encerrando WPPConnect Server...');
        
        for (const sessionName of this.sessions.keys()) {
          await this.closeSession(sessionName);
        }
        
        process.exit(0);
      });

    } catch (error) {
      this.logger.error('❌ Erro ao iniciar WPPConnect Server:', error);
      process.exit(1);
    }
  }
}

// Iniciar servidor se executado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new WPPConnectServer();
  server.start();
}

export default WPPConnectServer;
